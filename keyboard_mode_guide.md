# 指南针连接问题解决方案 - 键盘操作模式

## 问题解决

原来的连接失败是因为：
1. **软件路径错误**: 配置的 `C:\Program Files\Compass\Compass.exe` 不存在
2. **复杂控件依赖**: 原代码依赖具体的控件ID，容易失效
3. **窗口标题匹配失败**: 完整的窗口标题可能与实际不符

## 新的解决方案

### 1. 键盘操作模式优势
- ✅ **不依赖软件路径**: 无需配置准确的exe路径
- ✅ **不依赖控件ID**: 不需要复杂的控件定位
- ✅ **更稳定**: 使用通用的键盘操作，兼容性更好
- ✅ **OCR数据获取**: 通过截图识别获取数据，更可靠

### 2. 使用步骤

#### 第一步：手动启动指南针软件
```
1. 手动打开指南针软件（不管安装在哪里）
2. 确保软件窗口可见，并且标题包含"指南针"等关键词
```

#### 第二步：运行股票筛选器
```bash
python main.py
```

#### 第三步：点击"测试连接"
- 工具会自动查找包含"指南针"、"全赢"、"数据分析"等关键词的窗口
- 连接成功后，会使用键盘操作进行股票搜索

### 3. 工作原理

#### 连接方式
```python
# 支持模糊匹配多种窗口标题
possible_titles = ["指南针", "Compass", "全赢", "数据分析"]
```

#### 股票搜索方式
```python
# 简化的键盘操作（根据指南针软件实际使用方式）
window.set_focus()          # 激活窗口
window.type_keys("000001")  # 直接输入股票代码
window.type_keys("{ENTER}") # 回车确认
```

#### 数据获取方式
- **优先使用OCR模式**: 截图 + 文字识别
- **备用控件模式**: 如果OCR失败，回退到控件读取

### 4. 配置变化

#### config.py 主要更新
```python
# 启用OCR模式（更稳定）
'use_ocr_mode': True

# 简化窗口标题（支持模糊匹配）  
'main_window_title': '指南针'

# 软件路径变为可选（键盘模式下不必需）
'exe_path': r'C:\Program Files\Compass\Compass.exe'  # 仅供参考
```

### 5. 故障排除

#### 如果连接仍然失败
1. **检查软件是否运行**: 确保指南针软件已启动
2. **检查窗口标题**: 窗口标题应包含"指南针"等关键词
3. **手动测试键盘操作**: 在指南针软件中手动按Ctrl+F是否有反应

#### 如果OCR识别失败
1. **配置截图区域**: 使用界面中的"选择区域"功能
2. **检查区域设置**: 确保OCR区域包含资金数据显示区域
3. **调整OCR参数**: 可以修改置信度阈值等参数

### 6. 优势总结

通过这次修改：
- 🎯 **实现了您的目标**: 使用pywinauto模拟键盘操作
- 🔧 **简化了配置**: 不再需要复杂的软件路径和控件配置  
- 🚀 **提高了稳定性**: 键盘操作比控件定位更可靠
- 📈 **增强了兼容性**: 支持不同版本的指南针软件

现在您可以：
1. 手动启动指南针软件
2. 运行股票筛选器
3. 点击"测试连接"验证功能