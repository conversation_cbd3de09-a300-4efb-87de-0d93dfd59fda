# -*- coding: utf-8 -*-
"""
环境诊断脚本
检查程序运行所需的依赖和环境
"""

import sys
import os

def check_python_version():
    """检查Python版本"""
    print("=== Python环境检查 ===")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    version_info = sys.version_info
    if version_info.major >= 3 and version_info.minor >= 8:
        print("✓ Python版本符合要求 (>= 3.8)")
        return True
    else:
        print("✗ Python版本过低，建议使用Python 3.8+")
        return False

def check_dependencies():
    """检查依赖库"""
    print("\n=== 依赖库检查 ===")
    dependencies = [
        ('tkinter', 'GUI框架'),
        ('mss', '屏幕截图'),
        ('cv2', 'OpenCV图像处理'),
        ('PIL', 'Pillow图像库'),
        ('numpy', '数值计算'),
        ('pandas', '数据处理'),
        ('openpyxl', 'Excel处理'),
        ('psutil', '系统工具'),
        ('pywinauto', 'Windows自动化'),
    ]
    
    missing_deps = []
    optional_deps = []
    
    for module, desc in dependencies:
        try:
            __import__(module)
            print(f"✓ {module} ({desc})")
        except ImportError:
            print(f"✗ {module} ({desc}) - 缺失")
            missing_deps.append(module)
    
    # 检查可选依赖
    print("\n--- 可选依赖 (OCR功能) ---")
    optional = [
        ('easyocr', 'OCR文字识别'),
        ('torch', 'PyTorch深度学习框架'),
    ]
    
    for module, desc in optional:
        try:
            __import__(module)
            print(f"✓ {module} ({desc})")
        except ImportError:
            print(f"? {module} ({desc}) - 可选，OCR功能需要")
            optional_deps.append(module)
    
    return missing_deps, optional_deps

def check_files():
    """检查项目文件"""
    print("\n=== 项目文件检查 ===")
    required_files = [
        'main.py',
        'config.py', 
        'data_processor.py',
        'compass_automator.py',
        'region_selector.py',
        'fund_ocr.py',
        'requirements.txt'
    ]
    
    missing_files = []
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    for filename in required_files:
        filepath = os.path.join(current_dir, filename)
        if os.path.exists(filepath):
            print(f"✓ {filename}")
        else:
            print(f"✗ {filename} - 缺失")
            missing_files.append(filename)
    
    return missing_files

def generate_install_command(missing_deps):
    """生成安装命令"""
    if missing_deps:
        print(f"\n=== 安装建议 ===")
        print("缺失的依赖库，请运行以下命令安装:")
        print("pip install -r requirements.txt")
        print("\n或者单独安装:")
        for dep in missing_deps:
            print(f"pip install {dep}")

def main():
    """主函数"""
    print("股票筛选器环境诊断工具")
    print("=" * 50)
    
    # 检查Python版本
    python_ok = check_python_version()
    
    # 检查依赖
    missing_deps, optional_deps = check_dependencies()
    
    # 检查文件
    missing_files = check_files()
    
    # 总结
    print("\n=== 诊断总结 ===")
    if python_ok and not missing_deps and not missing_files:
        print("✓ 环境检查通过，可以运行程序")
        print("\n启动建议:")
        print("1. 运行主程序: python main.py")
        print("2. 运行测试程序: python test_region_ocr.py")
        
        if optional_deps:
            print(f"\n注意: OCR功能需要安装 {', '.join(optional_deps)}")
    else:
        print("✗ 发现问题:")
        if not python_ok:
            print("  - Python版本不符合要求")
        if missing_deps:
            print(f"  - 缺失依赖: {', '.join(missing_deps)}")
        if missing_files:
            print(f"  - 缺失文件: {', '.join(missing_files)}")
        
        generate_install_command(missing_deps)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n诊断脚本运行出错: {e}")
    finally:
        input("\n按回车键退出...")