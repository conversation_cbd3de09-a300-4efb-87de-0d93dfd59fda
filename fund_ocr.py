# -*- coding: utf-8 -*-
"""
增强OCR识别模块
专门用于识别屏幕区域中的多空资金数据
"""

import re
import logging
import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
import easyocr
import paddleocr
from PIL import Image
import mss
import time
import traceback
import os
from datetime import datetime

class FundDataOCR:
    """多空资金数据OCR识别器"""
    
    def __init__(self, use_gpu: bool = True, debug_mode: bool = False, save_debug_images: bool = False):
        """
        初始化OCR识别器
        
        Args:
            use_gpu: 是否使用GPU加速
            debug_mode: 是否启用调试模式
            save_debug_images: 是否保存调试图像
        """
        self.logger = logging.getLogger(__name__)
        self.reader = None
        self.paddle_reader = None
        self.use_gpu = use_gpu
        self.debug_mode = debug_mode
        self.save_debug_images = save_debug_images
        self.ocr_engines = {'easyocr': False, 'paddleocr': False}
        self.error_count = 0
        self.max_errors = 10  # 最大错误次数
        
        # 创建调试目录
        if self.save_debug_images:
            self.debug_dir = f"debug_images_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(self.debug_dir, exist_ok=True)
            
        self._init_ocr_engines()
    
    def _check_gpu_availability(self) -> bool:
        """
        检查GPU是否可用
        
        Returns:
            是否有可用的GPU
        """
        try:
            import torch
            gpu_available = torch.cuda.is_available() and torch.cuda.device_count() > 0
            if gpu_available:
                device_name = torch.cuda.get_device_name(0)
                self.logger.info(f"检测到GPU设备: {device_name}")
            else:
                self.logger.info("未检测到可用的GPU设备，将使用CPU模式")
            return gpu_available
        except ImportError:
            self.logger.info("PyTorch未安装或不支持CUDA，将使用CPU模式")
            return False
        except Exception as e:
            self.logger.warning(f"GPU检测过程中出现错误: {str(e)}，将使用CPU模式")
            return False

    def _init_ocr_engines(self):
        """初始化所有OCR引擎"""
        self.logger.info("开始初始化OCR引擎...")
        
        # 智能GPU检测
        gpu_available = self._check_gpu_availability()
        actual_use_gpu = self.use_gpu and gpu_available
        
        if self.use_gpu and not gpu_available:
            self.logger.info("用户指定使用GPU但系统无可用GPU，自动切换到CPU模式")
        
        # 初始化EasyOCR
        self._init_easyocr(actual_use_gpu)
        
        # 初始化PaddleOCR
        self._init_paddleocr(actual_use_gpu)
        
        # 检查是否有可用引擎
        available_engines = [engine for engine, status in self.ocr_engines.items() if status]
        if not available_engines:
            error_msg = "没有可用的OCR引擎！请检查安装和配置。"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
        else:
            self.logger.info(f"可用的OCR引擎: {', '.join(available_engines)}")
    
    def _init_easyocr(self, use_gpu: bool):
        """
        初始化EasyOCR引擎
        
        Args:
            use_gpu: 是否使用GPU
        """
        try:
            if self.debug_mode:
                self.logger.debug(f"初始化EasyOCR引擎 (GPU: {use_gpu})...")
            
            # 抑制EasyOCR的警告信息
            import warnings
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                self.reader = easyocr.Reader(['ch_sim', 'en'], gpu=use_gpu, verbose=False)
            
            self.ocr_engines['easyocr'] = True
            self.logger.info(f"EasyOCR引擎初始化成功 ({'GPU' if use_gpu else 'CPU'}模式)")
            
        except Exception as e:
            self.logger.error(f"EasyOCR引擎初始化失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"EasyOCR错误详情: {traceback.format_exc()}")
            
            # 如果GPU模式失败，尝试CPU模式
            if use_gpu:
                try:
                    self.logger.info("GPU模式失败，尝试CPU模式...")
                    import warnings
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        self.reader = easyocr.Reader(['ch_sim', 'en'], gpu=False, verbose=False)
                    
                    self.ocr_engines['easyocr'] = True
                    self.logger.info("EasyOCR引擎初始化成功 (CPU回退模式)")
                    
                except Exception as e2:
                    self.logger.error(f"EasyOCR CPU模式初始化也失败: {str(e2)}")
                    if self.debug_mode:
                        self.logger.debug(f"EasyOCR CPU模式错误详情: {traceback.format_exc()}")
    
    def _init_paddleocr(self, use_gpu: bool):
        """
        初始化PaddleOCR引擎
        
        Args:
            use_gpu: 是否使用GPU
        """
        try:
            if self.debug_mode:
                self.logger.debug(f"初始化PaddleOCR引擎 (GPU: {use_gpu})...")
            
            # 配置PaddleOCR参数
            paddle_config = {
                'use_angle_cls': True,
                'lang': 'ch',
                'show_log': False  # 减少日志输出
            }
            
            # 如果不使用GPU，明确设置use_gpu=False
            if not use_gpu:
                paddle_config['use_gpu'] = False
            
            self.paddle_reader = paddleocr.PaddleOCR(**paddle_config)
            self.ocr_engines['paddleocr'] = True
            self.logger.info(f"PaddleOCR引擎初始化成功 ({'GPU' if use_gpu else 'CPU'}模式)")
            
        except Exception as e:
            self.logger.error(f"PaddleOCR引擎初始化失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"PaddleOCR错误详情: {traceback.format_exc()}")
            
            # 如果GPU模式失败，尝试CPU模式
            if use_gpu:
                try:
                    self.logger.info("PaddleOCR GPU模式失败，尝试CPU模式...")
                    paddle_config = {
                        'use_angle_cls': True,
                        'lang': 'ch',
                        'show_log': False,
                        'use_gpu': False
                    }
                    self.paddle_reader = paddleocr.PaddleOCR(**paddle_config)
                    self.ocr_engines['paddleocr'] = True
                    self.logger.info("PaddleOCR引擎初始化成功 (CPU回退模式)")
                    
                except Exception as e2:
                    self.logger.error(f"PaddleOCR CPU模式初始化也失败: {str(e2)}")
                    if self.debug_mode:
                        self.logger.debug(f"PaddleOCR CPU模式错误详情: {traceback.format_exc()}")
    
    def _init_ocr(self):
        """向后兼容的初始化方法"""
        self._init_ocr_engines()
    
    def recognize_fund_data_in_region(self, x: int, y: int, width: int, height: int) -> Dict[str, float]:
        """
        识别指定区域中的多空资金数据
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域尺寸
            
        Returns:
            识别到的资金数据字典
        """
        try:
            if self.error_count >= self.max_errors:
                error_msg = f"错误次数过多({self.error_count})，停止OCR识别"
                self.logger.error(error_msg)
                return {'error': error_msg}
            
            if self.debug_mode:
                self.logger.debug(f"开始识别区域: x={x}, y={y}, width={width}, height={height}")
            
            # 截取指定区域
            screenshot = self._capture_region(x, y, width, height)
            if screenshot is None:
                self.error_count += 1
                error_msg = '截图失败'
                if self.debug_mode:
                    self.logger.debug(f"截图失败，区域: ({x},{y},{width},{height})")
                return {'error': error_msg}
            
            if self.debug_mode:
                self.logger.debug(f"截图成功，图像形状: {screenshot.shape}")
            
            # 保存原始截图（调试模式）
            if self.save_debug_images:
                self._save_debug_image(screenshot, f"original_{x}_{y}_{width}_{height}")
            
            # 预处理图像
            processed_image = self._preprocess_image(screenshot)
            
            if self.debug_mode:
                self.logger.debug(f"图像预处理完成，处理后形状: {processed_image.shape}")
            
            # 保存预处理后的图像（调试模式）
            if self.save_debug_images:
                self._save_debug_image(processed_image, f"processed_{x}_{y}_{width}_{height}")
            
            # 多引擎OCR识别
            fund_data = self._multi_engine_ocr(processed_image)
            
            # 如果标准识别未获得理想结果，尝试百分比专用识别
            if not fund_data or len(fund_data) == 0:
                if self.debug_mode:
                    self.logger.debug("标准识别未获得结果，尝试百分比专用识别...")
                
                percentage_data = self._percentage_specialized_recognition(screenshot, x, y, width, height)
                if percentage_data:
                    fund_data.update(percentage_data)
                    if self.debug_mode:
                        self.logger.debug(f"百分比专用识别获得结果: {percentage_data}")
            
            # 如果专用识别也失败，尝试备用方案
            if not fund_data or len(fund_data) == 0:
                if self.debug_mode:
                    self.logger.debug("百分比专用识别也未获得结果，尝试备用方案...")
                
                backup_data = self._backup_recognition(screenshot)
                if backup_data:
                    fund_data.update(backup_data)
            
            if self.debug_mode:
                self.logger.debug(f"区域 ({x},{y},{width},{height}) 识别到的资金数据: {fund_data}")
            
            # 重置错误计数（成功时）
            if fund_data and not fund_data.get('error'):
                self.error_count = 0
            
            return fund_data
            
        except Exception as e:
            self.error_count += 1
            error_msg = f"识别区域资金数据失败: {str(e)}"
            self.logger.error(error_msg)
            if self.debug_mode:
                self.logger.debug(f"错误详情: {traceback.format_exc()}")
            return {'error': error_msg}
    
    def _multi_engine_ocr(self, image: np.ndarray) -> Dict[str, float]:
        """
        使用多个OCR引擎进行识别
        
        Args:
            image: 预处理后的图像
            
        Returns:
            识别到的资金数据
        """
        all_results = {}
        
        # 尝试EasyOCR
        if self.ocr_engines['easyocr'] and self.reader:
            try:
                if self.debug_mode:
                    self.logger.debug(f"开始EasyOCR识别，图像形状: {image.shape}")
                
                easyocr_results = self.reader.readtext(image)
                
                if self.debug_mode:
                    self.logger.debug(f"EasyOCR原始结果: {easyocr_results}")
                    self.logger.debug(f"EasyOCR结果数量: {len(easyocr_results)}")
                    # 强制输出所有结果，不管置信度
                    print(f"\n=== EasyOCR识别结果详情 ===")
                    print(f"总共识别到 {len(easyocr_results)} 个文字区域:")
                    for i, result in enumerate(easyocr_results):
                        bbox, text, confidence = result
                        print(f"  {i+1}. 文字: '{text}' | 置信度: {confidence:.3f} | 位置: {bbox}")
                        self.logger.debug(f"结果{i+1}: 文本='{text}', 置信度={confidence:.3f}, 边界框={bbox}")
                    print("=" * 50)
                
                easyocr_data = self._parse_fund_data(easyocr_results, 'EasyOCR')
                if easyocr_data:
                    all_results.update(easyocr_data)
                    if self.debug_mode:
                        self.logger.debug(f"EasyOCR解析后的资金数据: {easyocr_data}")
                else:
                    if self.debug_mode:
                        self.logger.debug("EasyOCR未能解析出有效的资金数据")
            except Exception as e:
                self.logger.error(f"EasyOCR识别失败: {str(e)}")
                if self.debug_mode:
                    self.logger.debug(f"EasyOCR错误详情: {traceback.format_exc()}")
        
        # 尝试PaddleOCR
        if self.ocr_engines['paddleocr'] and self.paddle_reader:
            try:
                paddle_results = self.paddle_reader.ocr(image, cls=True)
                paddle_data = self._parse_paddle_results(paddle_results)
                if paddle_data:
                    # 合并结果，PaddleOCR结果作为补充
                    for key, value in paddle_data.items():
                        if key not in all_results:
                            all_results[key] = value
                    if self.debug_mode:
                        self.logger.debug(f"PaddleOCR识别结果: {paddle_data}")
            except Exception as e:
                self.logger.error(f"PaddleOCR识别失败: {str(e)}")
                if self.debug_mode:
                    self.logger.debug(f"PaddleOCR错误详情: {traceback.format_exc()}")
        
        return all_results
    
    def _percentage_specialized_recognition(self, image: np.ndarray, x: int, y: int, width: int, height: int) -> Dict[str, float]:
        """
        专门针对百分比数值的识别方法
        使用多种预处理策略和更宽松的识别参数
        
        Args:
            image: 原始图像
            x, y: 区域坐标（用于调试文件命名）
            width, height: 区域尺寸（用于调试文件命名）
            
        Returns:
            识别到的百分比数据
        """
        all_results = {}
        
        try:
            if self.debug_mode:
                self.logger.debug("开始百分比专用识别...")
            
            # 获取多种预处理策略的结果
            preprocessing_strategies = self._preprocess_for_percentage_multi_strategy(image)
            
            # 对每种预处理策略都进行OCR识别
            for strategy_name, processed_image in preprocessing_strategies:
                if self.debug_mode:
                    self.logger.debug(f"尝试预处理策略: {strategy_name}")
                
                # 保存预处理后的图像（调试模式）
                if self.save_debug_images:
                    self._save_debug_image(processed_image, f"percentage_{strategy_name}_{x}_{y}_{width}_{height}")
                
                # 使用更宽松的参数进行OCR识别
                strategy_results = self._multi_engine_ocr_with_relaxed_params(processed_image, strategy_name)
                
                # 合并结果
                if strategy_results:
                    for key, value in strategy_results.items():
                        if key not in all_results:  # 避免重复，保留第一个找到的结果
                            all_results[key] = value
                    
                    if self.debug_mode:
                        self.logger.debug(f"策略 {strategy_name} 识别到: {strategy_results}")
                
                # 如果已经找到足够的数据，可以提前退出
                if len(all_results) >= 3:  # 假设我们期望至少3个数据点
                    if self.debug_mode:
                        self.logger.debug(f"已获得足够数据，停止尝试其他预处理策略")
                    break
            
            if self.debug_mode:
                self.logger.debug(f"百分比专用识别最终结果: {all_results}")
            
            return all_results
            
        except Exception as e:
            self.logger.error(f"百分比专用识别失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"错误详情: {traceback.format_exc()}")
            return {}
    
    def _multi_engine_ocr_with_relaxed_params(self, image: np.ndarray, strategy_name: str = "unknown") -> Dict[str, float]:
        """
        使用更宽松参数的多引擎OCR识别
        专门用于百分比识别
        
        Args:
            image: 预处理后的图像
            strategy_name: 预处理策略名称（用于调试）
            
        Returns:
            识别到的数据
        """
        all_results = {}
        
        # 尝试EasyOCR（使用更宽松的参数）
        if self.ocr_engines['easyocr'] and self.reader:
            try:
                if self.debug_mode:
                    self.logger.debug(f"开始EasyOCR识别（策略: {strategy_name}）...")
                
                # 使用更宽松的参数
                easyocr_results = self.reader.readtext(
                    image,
                    allowlist='0123456789.-%+',  # 只允许这些字符，提高准确性
                    width_ths=0.3,  # 降低宽度阈值
                    height_ths=0.3,  # 降低高度阈值
                    paragraph=False  # 不合并段落
                )
                
                if self.debug_mode:
                    self.logger.debug(f"EasyOCR（{strategy_name}）结果数量: {len(easyocr_results)}")
                    for i, result in enumerate(easyocr_results):
                        bbox, text, confidence = result
                        self.logger.debug(f"  结果{i+1}: '{text}' (置信度: {confidence:.3f})")
                
                # 使用更宽松的置信度阈值解析结果
                easyocr_data = self._parse_fund_data_relaxed(easyocr_results, f'EasyOCR-{strategy_name}')
                if easyocr_data:
                    all_results.update(easyocr_data)
                    
            except Exception as e:
                if self.debug_mode:
                    self.logger.debug(f"EasyOCR（{strategy_name}）识别失败: {str(e)}")
        
        # 尝试PaddleOCR
        if self.ocr_engines['paddleocr'] and self.paddle_reader:
            try:
                if self.debug_mode:
                    self.logger.debug(f"开始PaddleOCR识别（策略: {strategy_name}）...")
                
                paddle_results = self.paddle_reader.ocr(image, cls=True)
                paddle_data = self._parse_paddle_results_relaxed(paddle_results, strategy_name)
                
                if paddle_data:
                    # 合并结果，避免覆盖已有的好结果
                    for key, value in paddle_data.items():
                        if key not in all_results:
                            all_results[key] = value
                    
                    if self.debug_mode:
                        self.logger.debug(f"PaddleOCR（{strategy_name}）识别结果: {paddle_data}")
                        
            except Exception as e:
                if self.debug_mode:
                    self.logger.debug(f"PaddleOCR（{strategy_name}）识别失败: {str(e)}")
        
        return all_results
    
    def _backup_recognition(self, image: np.ndarray) -> Dict[str, float]:
        """
        备用识别方案（使用不同的图像预处理）
        
        Args:
            image: 原始图像
            
        Returns:
            识别到的资金数据
        """
        try:
            # 尝试不同的预处理方法
            backup_methods = [
                self._preprocess_for_dark_text,
                self._preprocess_for_light_text,
                self._preprocess_simple
            ]
            
            for i, method in enumerate(backup_methods):
                try:
                    processed = method(image)
                    if self.save_debug_images:
                        self._save_debug_image(processed, f"backup_method_{i}")
                    
                    # 使用可用的OCR引擎
                    if self.ocr_engines['easyocr'] and self.reader:
                        results = self.reader.readtext(processed)
                        data = self._parse_fund_data(results, f'Backup-EasyOCR-{i}')
                        if data:
                            return data
                            
                except Exception as e:
                    if self.debug_mode:
                        self.logger.debug(f"备用方法{i}失败: {str(e)}")
                    continue
            
            return {}
            
        except Exception as e:
            self.logger.error(f"备用识别失败: {str(e)}")
            return {}
    
    def _preprocess_for_dark_text(self, image: np.ndarray) -> np.ndarray:
        """针对深色文字的预处理"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # 增强对比度
        enhanced = cv2.convertScaleAbs(gray, alpha=2.0, beta=50)
        # 阈值处理
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        return binary
    
    def _preprocess_for_light_text(self, image: np.ndarray) -> np.ndarray:
        """针对浅色文字的预处理"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # 反转颜色
        inverted = cv2.bitwise_not(gray)
        # 增强对比度
        enhanced = cv2.convertScaleAbs(inverted, alpha=1.5, beta=30)
        return enhanced
    
    def _preprocess_simple(self, image: np.ndarray) -> np.ndarray:
        """简单预处理"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        return blurred
    
    def _parse_paddle_results(self, paddle_results: List) -> Dict[str, float]:
        """
        解析PaddleOCR结果
        
        Args:
            paddle_results: PaddleOCR识别结果
            
        Returns:
            解析后的资金数据
        """
        try:
            if not paddle_results or not paddle_results[0]:
                return {}
            
            # 提取文本
            texts = []
            for line in paddle_results[0]:
                if len(line) >= 2:
                    text = line[1][0]  # 获取识别的文本
                    confidence = line[1][1]  # 获取置信度
                    if confidence >= 0.5:  # 置信度阈值
                        texts.append(text)
            
            combined_text = ' '.join(texts)
            if self.debug_mode:
                self.logger.debug(f"PaddleOCR识别文本: {combined_text}")
            
            # 使用相同的解析逻辑
            return self._parse_text_for_fund_data(combined_text)
            
        except Exception as e:
            self.logger.error(f"解析PaddleOCR结果失败: {str(e)}")
            return {}
    
    def _save_debug_image(self, image: np.ndarray, filename: str):
        """
        保存调试图像
        
        Args:
            image: 图像数组
            filename: 文件名
        """
        if not self.save_debug_images:
            return
            
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
            full_filename = f"{filename}_{timestamp}.png"
            file_path = os.path.join(self.debug_dir, full_filename)
            cv2.imwrite(file_path, image)
            if self.debug_mode:
                self.logger.debug(f"调试图像已保存: {file_path}")
        except Exception as e:
            self.logger.error(f"保存调试图像失败: {str(e)}")
    
    def _capture_region(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """
        截取屏幕指定区域
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域尺寸
            
        Returns:
            图像数组或None
        """
        try:
            with mss.mss() as sct:
                # 定义截图区域
                monitor = {
                    "top": y,
                    "left": x,
                    "width": width,
                    "height": height
                }
                
                # 截图
                screenshot = sct.grab(monitor)
                
                # 转换为numpy数组
                img_array = np.array(screenshot)
                
                # 转换颜色格式 (BGRA -> BGR)
                img_bgr = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
                
                return img_bgr
                
        except Exception as e:
            self.logger.error(f"截取区域失败: {str(e)}")
            return None
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理以提高OCR识别率
        
        Args:
            image: 原始图像
            
        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始图像预处理...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            if self.debug_mode:
                self.logger.debug("已转换为灰度图")
            
            # 调整亮度和对比度
            gray = cv2.convertScaleAbs(gray, alpha=1.5, beta=30)
            
            if self.debug_mode:
                self.logger.debug("已调整亮度和对比度")
            
            # 应用高斯滤波去噪
            denoised = cv2.GaussianBlur(gray, (3, 3), 0)
            
            if self.debug_mode:
                self.logger.debug("已应用高斯滤波")
            
            # 自适应阈值处理
            binary = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            if self.debug_mode:
                self.logger.debug("已完成自适应阈值处理")
            
            # 形态学操作以改善字符连接
            kernel = np.ones((2, 2), np.uint8)
            processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            if self.debug_mode:
                self.logger.debug("图像预处理完成")
            
            return processed
            
        except Exception as e:
            self.logger.error(f"图像预处理失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"预处理错误详情: {traceback.format_exc()}")
            return image

    def _preprocess_for_percentage(self, image: np.ndarray) -> np.ndarray:
        """
        专门针对百分比识别的预处理方法
        保护负号、小数点、百分号等特殊字符
        
        Args:
            image: 原始图像
            
        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始百分比专用预处理...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 图像尺寸放大（提高小字符识别率）
            scale_factor = 2.0
            height, width = gray.shape
            new_height, new_width = int(height * scale_factor), int(width * scale_factor)
            scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # 非常轻微的对比度增强（避免字符变形）
            enhanced = cv2.convertScaleAbs(scaled, alpha=1.1, beta=5)
            
            # 应用双边滤波去噪（保护边缘）
            denoised = cv2.bilateralFilter(enhanced, 5, 25, 25)
            
            # 使用OTSU阈值（自动选择最佳阈值）
            _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            if self.debug_mode:
                self.logger.debug("百分比专用预处理完成")
            
            return binary
            
        except Exception as e:
            self.logger.error(f"百分比预处理失败: {str(e)}")
            return image
    
    def _preprocess_for_percentage_v2(self, image: np.ndarray) -> np.ndarray:
        """
        百分比预处理方法 v2 - 保守策略
        最小化处理，保护字符完整性
        
        Args:
            image: 原始图像
            
        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始百分比预处理v2（保守策略）...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 轻微缩放提升分辨率
            scale_factor = 1.5
            height, width = gray.shape
            new_height, new_width = int(height * scale_factor), int(width * scale_factor)
            scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            # 轻微锐化（增强字符边缘）
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(scaled, -1, kernel * 0.5) # 减弱锐化强度
            
            # 限制在合理范围内
            result = np.clip(sharpened, 0, 255).astype(np.uint8)
            
            if self.debug_mode:
                self.logger.debug("百分比预处理v2完成")
            
            return result
            
        except Exception as e:
            self.logger.error(f"百分比预处理v2失败: {str(e)}")
            return image
    
    def _preprocess_for_percentage_v3(self, image: np.ndarray) -> np.ndarray:
        """
        百分比预处理方法 v3 - 激进策略
        针对低质量图像的强化处理
        
        Args:
            image: 原始图像
            
        Returns:
            处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("开始百分比预处理v3（激进策略）...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 大幅缩放
            scale_factor = 3.0
            height, width = gray.shape
            new_height, new_width = int(height * scale_factor), int(width * scale_factor)
            scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # 强化对比度
            enhanced = cv2.convertScaleAbs(scaled, alpha=1.3, beta=15)
            
            # 高斯模糊去噪
            blurred = cv2.GaussianBlur(enhanced, (3, 3), 0)
            
            # 自适应阈值
            binary = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 9, 3
            )
            
            # 形态学闭操作（连接字符断裂部分）
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
            closed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            if self.debug_mode:
                self.logger.debug("百分比预处理v3完成")
            
            return closed
            
        except Exception as e:
            self.logger.error(f"百分比预处理v3失败: {str(e)}")
            return image
    
    def _preprocess_for_percentage_multi_strategy(self, image: np.ndarray) -> List[np.ndarray]:
        """
        多策略百分比预处理
        生成多个不同预处理策略的结果
        
        Args:
            image: 原始图像
            
        Returns:
            多个预处理结果的列表
        """
        strategies = []
        
        try:
            # 策略1：标准预处理
            result1 = self._preprocess_for_percentage(image)
            strategies.append(('standard', result1))
            
            # 策略2：保守预处理
            result2 = self._preprocess_for_percentage_v2(image)
            strategies.append(('conservative', result2))
            
            # 策略3：激进预处理
            result3 = self._preprocess_for_percentage_v3(image)
            strategies.append(('aggressive', result3))
            
            # 策略4：无预处理（只放大）
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            scaled_only = cv2.resize(gray, None, fx=2.0, fy=2.0, interpolation=cv2.INTER_CUBIC)
            strategies.append(('scale_only', scaled_only))
            
            if self.debug_mode:
                self.logger.debug(f"生成了{len(strategies)}种预处理策略")
            
            return strategies
            
        except Exception as e:
            self.logger.error(f"多策略预处理失败: {str(e)}")
            # 返回原图作为后备
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            return [('fallback', gray)]
    
    def _preprocess_image_simple(self, image: np.ndarray) -> np.ndarray:
        """
        简化的图像预处理（用于测试）
        
        Args:
            image: 原始图像
            
        Returns:
            简单处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("使用简化预处理...")
            
            # 只进行灰度转换
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 轻微增强对比度
            enhanced = cv2.convertScaleAbs(gray, alpha=1.2, beta=10)
            
            if self.debug_mode:
                self.logger.debug("简化预处理完成")
            
            return enhanced
            
        except Exception as e:
            self.logger.error(f"简化预处理失败: {str(e)}")
            return image
    
    def _preprocess_image_cpu_optimized(self, image: np.ndarray) -> np.ndarray:
        """
        针对CPU模式优化的图像预处理
        
        Args:
            image: 原始图像
            
        Returns:
            优化处理后的图像
        """
        try:
            if self.debug_mode:
                self.logger.debug("使用CPU优化预处理...")
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 适中的对比度增强（不要过度）
            enhanced = cv2.convertScaleAbs(gray, alpha=1.3, beta=20)
            
            # 轻微的高斯模糊去噪（核心更小）
            denoised = cv2.GaussianBlur(enhanced, (1, 1), 0)
            
            if self.debug_mode:
                self.logger.debug("CPU优化预处理完成")
            
            return denoised
            
        except Exception as e:
            self.logger.error(f"CPU优化预处理失败: {str(e)}")
            return image
    
    def test_preprocessing_methods(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        测试不同预处理方法的效果
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域尺寸
            
        Returns:
            不同预处理方法的识别结果对比
        """
        if self.debug_mode:
            self.logger.debug("开始测试不同预处理方法...")
        
        results = {}
        
        # 截取原始图像
        screenshot = self._capture_region(x, y, width, height)
        if screenshot is None:
            return {'error': '截图失败'}
        
        # 保存原始图像
        if self.save_debug_images:
            self._save_debug_image(screenshot, f"original_test_{x}_{y}_{width}_{height}")
        
        # 测试1: 无预处理
        if self.debug_mode:
            self.logger.debug("测试无预处理识别...")
        try:
            no_process_result = self._multi_engine_ocr(screenshot)
            results['no_preprocessing'] = no_process_result
            if self.save_debug_images:
                self._save_debug_image(screenshot, f"no_process_{x}_{y}_{width}_{height}")
        except Exception as e:
            results['no_preprocessing'] = {'error': str(e)}
        
        # 测试2: 简化预处理
        if self.debug_mode:
            self.logger.debug("测试简化预处理识别...")
        try:
            simple_processed = self._preprocess_image_simple(screenshot)
            simple_result = self._multi_engine_ocr(simple_processed)
            results['simple_preprocessing'] = simple_result
            if self.save_debug_images:
                self._save_debug_image(simple_processed, f"simple_{x}_{y}_{width}_{height}")
        except Exception as e:
            results['simple_preprocessing'] = {'error': str(e)}
        
        # 测试3: 完整预处理
        if self.debug_mode:
            self.logger.debug("测试完整预处理识别...")
        try:
            full_processed = self._preprocess_image(screenshot)
            full_result = self._multi_engine_ocr(full_processed)
            results['full_preprocessing'] = full_result
            if self.save_debug_images:
                self._save_debug_image(full_processed, f"full_{x}_{y}_{width}_{height}")
        except Exception as e:
            results['full_preprocessing'] = {'error': str(e)}
        
        # 测试4: 只灰度化
        if self.debug_mode:
            self.logger.debug("测试只灰度化识别...")
        try:
            gray_only = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            gray_result = self._multi_engine_ocr(gray_only)
            results['gray_only'] = gray_result
            if self.save_debug_images:
                self._save_debug_image(gray_only, f"gray_{x}_{y}_{width}_{height}")
        except Exception as e:
            results['gray_only'] = {'error': str(e)}
        
        # 测试5: CPU优化预处理
        if self.debug_mode:
            self.logger.debug("测试CPU优化预处理识别...")
        try:
            cpu_optimized = self._preprocess_image_cpu_optimized(screenshot)
            cpu_result = self._multi_engine_ocr(cpu_optimized)
            results['cpu_optimized'] = cpu_result
            if self.save_debug_images:
                self._save_debug_image(cpu_optimized, f"cpu_opt_{x}_{y}_{width}_{height}")
        except Exception as e:
            results['cpu_optimized'] = {'error': str(e)}
            
        # 测试6: 百分比专用预处理
        if self.debug_mode:
            self.logger.debug("测试百分比专用预处理识别...")
        try:
            percentage_processed = self._preprocess_for_percentage(screenshot)
            percentage_result = self._multi_engine_ocr(percentage_processed)
            results['percentage_specialized'] = percentage_result
            if self.save_debug_images:
                self._save_debug_image(percentage_processed, f"percentage_{x}_{y}_{width}_{height}")
        except Exception as e:
            results['percentage_specialized'] = {'error': str(e)}
        
        if self.debug_mode:
            self.logger.debug(f"预处理测试完成，结果: {results}")
        
        return results
    
    def visualize_capture_region(self, x: int, y: int, width: int, height: int) -> bool:
        """
        可视化截取区域，在截图上标记指定区域
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域尺寸
            
        Returns:
            是否成功保存了可视化图像
        """
        try:
            if self.debug_mode:
                self.logger.debug(f"开始可视化截取区域: ({x}, {y}, {width}, {height})")
            
            # 截取更大的区域用于显示上下文
            context_padding = 50
            context_x = max(0, x - context_padding)
            context_y = max(0, y - context_padding)
            context_width = width + 2 * context_padding
            context_height = height + 2 * context_padding
            
            # 截取上下文区域
            context_image = self._capture_region(context_x, context_y, context_width, context_height)
            if context_image is None:
                if self.debug_mode:
                    self.logger.debug("截取上下文区域失败")
                return False
            
            # 在图像上绘制目标区域框
            visualized = context_image.copy()
            
            # 计算目标区域在上下文图像中的相对位置
            rel_x = x - context_x
            rel_y = y - context_y
            
            # 绘制红色边框标记目标区域
            cv2.rectangle(visualized, 
                         (rel_x, rel_y), 
                         (rel_x + width, rel_y + height), 
                         (0, 0, 255),  # 红色BGR
                         2)  # 线条粗细
            
            # 添加标签文本
            label = f"Target: {width}x{height}"
            cv2.putText(visualized, label, 
                       (rel_x, rel_y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 
                       0.6, (0, 0, 255), 2)
            
            # 保存可视化图像
            if self.save_debug_images:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
                filename = f"region_visualization_{x}_{y}_{width}_{height}_{timestamp}.png"
                filepath = os.path.join(self.debug_dir, filename)
                cv2.imwrite(filepath, visualized)
                
                if self.debug_mode:
                    self.logger.debug(f"区域可视化图像已保存: {filepath}")
                
                print(f"区域可视化图像已保存: {filepath}")
                print(f"红色框标记的是目标截取区域 ({x}, {y}, {width}, {height})")
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"可视化截取区域失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"可视化错误详情: {traceback.format_exc()}")
            return False
    
    def _parse_fund_data(self, ocr_results: List, engine_name: str = "Unknown") -> Dict[str, float]:
        """
        解析OCR结果，提取多空资金数据
        
        Args:
            ocr_results: OCR识别结果
            engine_name: OCR引擎名称
            
        Returns:
            解析后的资金数据
        """
        fund_data = {}
        
        try:
            if not ocr_results:
                if self.debug_mode:
                    self.logger.debug(f"{engine_name}: OCR结果为空")
                return {}
            
            # 过滤低置信度结果（降低阈值以捕获更多结果）
            filtered_results = []
            for result in ocr_results:
                if len(result) >= 3 and result[2] >= 0.01:  # 降低置信度阈值到0.01
                    filtered_results.append(result)
                elif self.debug_mode:
                    self.logger.debug(f"{engine_name}: 过滤低置信度结果: {result}")
            
            if not filtered_results:
                if self.debug_mode:
                    self.logger.debug(f"{engine_name}: 没有高置信度的OCR结果")
                return {}
            
            # 提取所有文本
            all_texts = [result[1] for result in filtered_results]
            combined_text = ' '.join(all_texts)
            
            if self.debug_mode:
                self.logger.debug(f"{engine_name} OCR识别文本: {combined_text}")
            
            # 解析文本数据
            fund_data = self._parse_text_for_fund_data(combined_text)
            
            if self.debug_mode and fund_data:
                self.logger.debug(f"{engine_name} 解析结果: {fund_data}")
            
            return fund_data
            
        except Exception as e:
            self.logger.error(f"{engine_name} 解析OCR结果失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"{engine_name} 错误详情: {traceback.format_exc()}")
            return {}
    
    def _parse_text_for_fund_data(self, text: str) -> Dict[str, float]:
        """
        从文本中解析多空资金数据
        
        Args:
            text: 输入文本
            
        Returns:
            解析后的资金数据
        """
        fund_data = {}
        
        try:
            # 查找百分比数据（多空资金通常以百分比形式显示）
            percentage_values = self._extract_percentage_values(text)
            
            # 查找金额数据
            amount_values = self._extract_amount_values(text)
            
            # 根据找到的数据数量分配到不同时期
            if percentage_values:
                fund_data.update(self._assign_percentage_data(percentage_values))
            
            if amount_values:
                fund_data.update(self._assign_amount_data(amount_values))
            
            return fund_data
            
        except Exception as e:
            self.logger.error(f"解析文本数据失败: {str(e)}")
            return {}
    
    def _extract_percentage_values(self, text: str) -> List[float]:
        """
        提取文本中的百分比值
        
        Args:
            text: 输入文本
            
        Returns:
            百分比值列表
        """
        values = []
        
        # 先进行OCR错误修正
        corrected_text = self._correct_ocr_errors(text)
        
        # 匹配百分比模式：如 -1.777%, 0.221%, +2.5% 等
        percentage_patterns = [
            r'([+-]?\d+\.?\d*)\s*%',  # 标准百分比
            r'([+-]?\d+\.?\d*)\s*％',  # 中文百分号
        ]
        
        # 只使用第一个成功匹配的模式，避免重复
        for pattern in percentage_patterns:
            matches = re.findall(pattern, corrected_text)
            if matches:  # 如果找到匹配，就不再尝试其他模式
                for match in matches:
                    try:
                        value = float(match)
                        values.append(value)
                    except ValueError:
                        continue
                break  # 跳出循环，避免重复匹配
        
        # 去重并排序
        unique_values = list(set(values))
        unique_values.sort(key=abs, reverse=True)  # 按绝对值降序排列
        
        if self.debug_mode:
            self.logger.debug(f"原始文本: '{text}'")
            self.logger.debug(f"修正后文本: '{corrected_text}'")
            self.logger.debug(f"提取到的百分比值: {unique_values}")
        
        return unique_values
    
    def _correct_ocr_errors(self, text: str) -> str:
        """
        修正常见的OCR识别错误（基于通用模式，不硬编码具体数值）
        
        Args:
            text: 原始OCR文本
            
        Returns:
            修正后的文本
        """
        corrected = text.strip()
        correction_applied = False
        correction_reason = ""
        
        # 通用OCR错误修正规则（按优先级排序）
        corrections = [
            # 1. 特殊字符修正（最高优先级，明确的字符替换）
            (r'#', '%', "特殊字符修正: # → %"),
            
            # 2. 明确的末尾字符错误（当数字后跟明显错误的字符时）
            (r'(\d+\.?\d*)\s*09$', r'\1%', "末尾字符修正: 09 → %"),
            (r'(\d+\.?\d*)\s*06$', r'\1%', "末尾字符修正: 06 → %"),
            
            # 3. 负号修正
            (r'^[I|l1]\s*(\d+\.?\d*)', r'-\1', "负号修正: I/l/1 → -"),
            (r'^[-–—]\s*(\d+\.?\d*)', r'-\1', "负号修正: 各种破折号 → -"),
            
            # 4. 零识别错误  
            (r'\bO(\d)', r'0\1', "字母O修正为数字0"),
            (r'(\d)O\b', r'\g<1>0', "字母O修正为数字0"),
            
            # 5. 空格可能是小数点（最低优先级，需要严格验证）
            # 只有当整个文本看起来像是 "数字 空格 少量数字" 时才考虑
            (r'^(\d{1,2})\s+(\d{1,3})$', r'\1.\2', "空格转小数点（需验证合理性）"),
        ]
        
        # 应用修正规则
        for pattern, replacement, reason in corrections:
            old_text = corrected
            corrected = re.sub(pattern, replacement, corrected)
            if old_text != corrected:
                correction_applied = True
                correction_reason = reason
                if self.debug_mode:
                    self.logger.debug(f"OCR修正: '{old_text}' -> '{corrected}' ({reason})")
                break  # 只应用第一个匹配的修正
        
        # 如果应用了空格转小数点的修正，需要验证合理性
        if "空格转小数点" in correction_reason:
            if not self._validate_percentage_format(corrected):
                if self.debug_mode:
                    self.logger.debug(f"小数点修正验证失败，恢复原文本: '{corrected}' -> '{text.strip()}'")
                corrected = text.strip()
                correction_applied = False
        
        # 如果没有应用任何修正，记录原始文本
        if not correction_applied and self.debug_mode:
            self.logger.debug(f"OCR文本无需修正: '{text}' -> '{corrected}'")
        
        return corrected
    
    def _validate_percentage_format(self, text: str) -> bool:
        """
        验证修正后的百分比格式是否合理
        
        Args:
            text: 修正后的文本
            
        Returns:
            是否是合理的百分比格式
        """
        try:
            # 检查是否包含数字
            if not re.search(r'\d', text):
                return False
            
            # 提取数值部分
            match = re.search(r'([+-]?\d+\.?\d*)', text)
            if not match:
                return False
            
            value = float(match.group(1))
            
            # 百分比值的合理范围检查（-100% 到 100%，股票数据中很少超出这个范围）
            if abs(value) > 100:
                if self.debug_mode:
                    self.logger.debug(f"百分比值超出常见范围: {value}% (>100%)")
                return False
            
            # 小数位数检查（通常不超过3位小数）
            decimal_part = match.group(1).split('.')
            if len(decimal_part) > 1 and len(decimal_part[1]) > 3:
                if self.debug_mode:
                    self.logger.debug(f"小数位数过多: {decimal_part[1]} (>3位)")
                return False
            
            # 对于空格转小数点的情况，额外检查合理性
            # 小数点前通常是1-2位数字，小数点后通常是1-3位数字
            if '.' in match.group(1):
                integer_part, decimal_part = match.group(1).split('.')
                if len(integer_part) > 2 or len(decimal_part) > 3:
                    if self.debug_mode:
                        self.logger.debug(f"数字格式不符合百分比常见格式: {match.group(1)}")
                    return False
            
            return True
            
        except (ValueError, AttributeError):
            return False
    
    def _extract_amount_values(self, text: str) -> List[float]:
        """
        提取文本中的金额值
        
        Args:
            text: 输入文本
            
        Returns:
            金额值列表（单位：万元）
        """
        values = []
        
        # 匹配各种金额格式
        amount_patterns = [
            r'([+-]?\d+\.?\d*)\s*万',     # 万元
            r'([+-]?\d+\.?\d*)\s*亿',     # 亿元  
            r'([+-]?\d+\.?\d*)\s*万元',   # 万元（完整）
            r'([+-]?\d+\.?\d*)\s*亿元',   # 亿元（完整）
            r'([+-]?\d+\.?\d*)\s*W',      # 英文万
            r'([+-]?\d+\.?\d*)\s*Y',      # 英文亿
        ]
        
        for pattern in amount_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    value = float(match)
                    # 统一转换为万元
                    if '亿' in pattern or 'Y' in pattern:
                        value *= 10000  # 亿转万
                    values.append(value)
                except ValueError:
                    continue
        
        # 去重并排序
        unique_values = list(set(values))
        unique_values.sort(key=abs, reverse=True)
        
        self.logger.debug(f"提取到的金额值: {unique_values}")
        return unique_values
    
    def _assign_percentage_data(self, values: List[float]) -> Dict[str, float]:
        """
        将百分比数据分配到不同时期
        
        Args:
            values: 百分比值列表
            
        Returns:
            分配后的数据字典
        """
        data = {}
        
        # 根据数量分配数据
        if len(values) >= 3:
            # 假设按顺序为：今天、昨天、前天
            data['today_fund_pct'] = values[0]
            data['yesterday_fund_pct'] = values[1]
            data['day_before_yesterday_fund_pct'] = values[2]
        elif len(values) == 2:
            # 只有两个值，分配给今天和昨天
            data['today_fund_pct'] = values[0]
            data['yesterday_fund_pct'] = values[1]
        elif len(values) == 1:
            # 只有一个值，分配给今天
            data['today_fund_pct'] = values[0]
        
        return data
    
    def _assign_amount_data(self, values: List[float]) -> Dict[str, float]:
        """
        将金额数据分配到不同时期
        
        Args:
            values: 金额值列表
            
        Returns:
            分配后的数据字典
        """
        data = {}
        
        # 根据数量分配数据
        if len(values) >= 3:
            data['today_fund'] = values[0]
            data['yesterday_fund'] = values[1]
            data['day_before_yesterday_fund'] = values[2]
        elif len(values) == 2:
            data['today_fund'] = values[0]
            data['yesterday_fund'] = values[1]
        elif len(values) == 1:
            data['today_fund'] = values[0]
        
        return data
    
    def get_engine_status(self) -> Dict[str, Any]:
        """
        获取OCR引擎状态
        
        Returns:
            引擎状态信息
        """
        return {
            'available_engines': self.ocr_engines,
            'error_count': self.error_count,
            'max_errors': self.max_errors,
            'debug_mode': self.debug_mode,
            'save_debug_images': self.save_debug_images,
            'debug_dir': getattr(self, 'debug_dir', None)
        }
    
    def reset_error_count(self):
        """重置错误计数"""
        self.error_count = 0
        self.logger.info("错误计数已重置")
    
    def test_ocr_with_sample_image(self) -> Dict[str, Any]:
        """
        使用示例图像测试OCR功能
        
        Returns:
            测试结果
        """
        try:
            # 创建测试图像
            test_img = np.ones((100, 300, 3), dtype=np.uint8) * 255
            
            # 添加文本（使用OpenCV绘制）
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(test_img, "123.45万", (50, 50), font, 1, (0, 0, 0), 2)
            
            # 保存测试图像
            if self.save_debug_images:
                self._save_debug_image(test_img, "test_sample")
            
            # 使用多引擎识别
            results = self._multi_engine_ocr(test_img)
            
            return {
                'success': True,
                'results': results,
                'engine_status': self.get_engine_status()
            }
            
        except Exception as e:
            error_msg = f"OCR测试失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'engine_status': self.get_engine_status()
            }

    def save_debug_image(self, image: np.ndarray, filename: str):
        """
        保存调试图像（向后兼容方法）
        
        Args:
            image: 图像数组
            filename: 文件名
        """
        self._save_debug_image(image, filename)
    
    def _parse_fund_data_relaxed(self, ocr_results: List, engine_name: str = "Unknown") -> Dict[str, float]:
        """
        使用更宽松参数解析OCR结果，专门用于百分比识别
        
        Args:
            ocr_results: OCR识别结果
            engine_name: OCR引擎名称
            
        Returns:
            解析后的资金数据
        """
        fund_data = {}
        
        try:
            if not ocr_results:
                if self.debug_mode:
                    self.logger.debug(f"{engine_name}: OCR结果为空")
                return {}
            
            # 使用极低的置信度阈值（对于百分比识别）
            filtered_results = []
            for result in ocr_results:
                if len(result) >= 3 and result[2] >= 0.001:  # 极低置信度阈值
                    filtered_results.append(result)
                elif self.debug_mode:
                    self.logger.debug(f"{engine_name}: 过滤极低置信度结果: {result}")
            
            if not filtered_results:
                if self.debug_mode:
                    self.logger.debug(f"{engine_name}: 没有可用的OCR结果")
                return {}
            
            # 提取所有文本并尝试多种组合
            all_texts = [result[1] for result in filtered_results]
            
            # 单独解析每个文本片段
            for text in all_texts:
                parsed_data = self._parse_text_for_percentage_only(text)
                if parsed_data:
                    fund_data.update(parsed_data)
            
            # 尝试组合文本解析
            combined_text = ' '.join(all_texts)
            combined_data = self._parse_text_for_percentage_only(combined_text)
            if combined_data:
                fund_data.update(combined_data)
            
            if self.debug_mode and fund_data:
                self.logger.debug(f"{engine_name} 宽松解析结果: {fund_data}")
            
            return fund_data
            
        except Exception as e:
            self.logger.error(f"{engine_name} 宽松解析OCR结果失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"{engine_name} 错误详情: {traceback.format_exc()}")
            return {}
    
    def _parse_paddle_results_relaxed(self, paddle_results: List, strategy_name: str = "unknown") -> Dict[str, float]:
        """
        使用更宽松参数解析PaddleOCR结果
        
        Args:
            paddle_results: PaddleOCR识别结果
            strategy_name: 策略名称
            
        Returns:
            解析后的资金数据
        """
        try:
            if not paddle_results or not paddle_results[0]:
                return {}
            
            # 提取文本，使用更宽松的置信度
            texts = []
            for line in paddle_results[0]:
                if len(line) >= 2:
                    text = line[1][0]  # 获取识别的文本
                    confidence = line[1][1]  # 获取置信度
                    if confidence >= 0.001:  # 极低置信度阈值
                        texts.append(text)
                        if self.debug_mode:
                            self.logger.debug(f"PaddleOCR（{strategy_name}）文本: '{text}' (置信度: {confidence:.3f})")
            
            # 单独和组合解析
            all_data = {}
            
            # 单独解析每个文本
            for text in texts:
                parsed_data = self._parse_text_for_percentage_only(text)
                if parsed_data:
                    all_data.update(parsed_data)
            
            # 组合解析
            combined_text = ' '.join(texts)
            combined_data = self._parse_text_for_percentage_only(combined_text)
            if combined_data:
                all_data.update(combined_data)
            
            if self.debug_mode:
                self.logger.debug(f"PaddleOCR（{strategy_name}）宽松解析结果: {all_data}")
            
            return all_data
            
        except Exception as e:
            self.logger.error(f"PaddleOCR（{strategy_name}）宽松解析失败: {str(e)}")
            return {}
    
    def _parse_text_for_percentage_only(self, text: str) -> Dict[str, float]:
        """
        专门解析百分比数据的方法
        
        Args:
            text: 输入文本
            
        Returns:
            解析后的百分比数据
        """
        fund_data = {}
        
        try:
            # 先进行OCR错误修正
            corrected_text = self._correct_ocr_errors_enhanced(text)
            
            # 专门查找百分比模式，使用更多变体
            percentage_patterns = [
                r'([+-]?\d+\.?\d*)\s*%',           # 标准百分比
                r'([+-]?\d+\.?\d*)\s*％',           # 中文百分号
                r'([+-]?\d+\.?\d*)\s*percent',     # 英文percent
                r'([+-]?\d+\.?\d*)\s*pct',         # 缩写pct
                r'([+-]?\d+)\s+(\d+)\s*%',         # 分离的整数和小数（如"0 222%"）
                r'([+-]?\d+)\s+(\d+)\s*％',        # 中文版本
            ]
            
            values = []
            
            # 首先尝试标准模式
            for pattern in percentage_patterns[:4]:  # 前4个是标准模式
                matches = re.findall(pattern, corrected_text, re.IGNORECASE)
                if matches:
                    for match in matches:
                        try:
                            value = float(match)
                            if self._is_reasonable_percentage(value):
                                values.append(value)
                                if self.debug_mode:
                                    self.logger.debug(f"找到百分比值: {value}% (模式: {pattern})")
                        except ValueError:
                            continue
                    if values:  # 如果找到了，就不再尝试其他模式
                        break
            
            # 如果标准模式没找到，尝试分离模式
            if not values:
                for pattern in percentage_patterns[4:]:  # 分离模式
                    matches = re.findall(pattern, corrected_text, re.IGNORECASE)
                    if matches:
                        for match in matches:
                            try:
                                if len(match) == 2:  # 分离的整数和小数
                                    value = float(f"{match[0]}.{match[1]}")
                                else:
                                    value = float(match)
                                
                                if self._is_reasonable_percentage(value):
                                    values.append(value)
                                    if self.debug_mode:
                                        self.logger.debug(f"找到分离百分比值: {value}% (模式: {pattern})")
                            except ValueError:
                                continue
                        if values:
                            break
            
            # 分配百分比数据到不同时期
            if values:
                fund_data = self._assign_percentage_data(values)
                if self.debug_mode:
                    self.logger.debug(f"百分比专用解析 - 原文本: '{text}' -> 修正文本: '{corrected_text}' -> 值: {values} -> 结果: {fund_data}")
            
            return fund_data
            
        except Exception as e:
            if self.debug_mode:
                self.logger.debug(f"百分比专用解析失败: {str(e)}")
            return {}
    
    def _correct_ocr_errors_enhanced(self, text: str) -> str:
        """
        增强版OCR错误修正，专门针对百分比识别
        
        Args:
            text: 原始OCR文本
            
        Returns:
            修正后的文本
        """
        corrected = text.strip()
        
        # 百分比专用修正规则
        enhanced_corrections = [
            # 百分号修正
            (r'%+', '%', "多个百分号修正"),
            (r'#', '%', "特殊字符修正: # → %"),
            (r'％', '%', "中文百分号修正"),
            (r'°', '%', "度数符号修正"),
            
            # 数字修正
            (r'[oO]', '0', "字母O修正为数字0"),
            (r'[Il|](\d)', r'1\1', "I/l/|修正为数字1"),
            (r'[Ss]', '5', "字母S修正为数字5"),
            
            # 负号修正
            (r'^[I|l1]\s*', '-', "负号修正: I/l/1 → -"),
            (r'^[-–—]\s*', '-', "负号修正: 各种破折号 → -"),
            (r'^[\[\(]\s*', '-', "左括号修正为负号"),
            
            # 小数点修正
            (r'[,，]', '.', "逗号修正为小数点"),
            (r'。', '.', "句号修正为小数点"),
            
            # 空格处理
            (r'\s+', '', "删除多余空格"),
            
            # 特殊的数字分离情况
            (r'(\d)\s+(\d)', r'\1.\2', "数字间空格修正为小数点"),
        ]
        
        for pattern, replacement, reason in enhanced_corrections:
            old_text = corrected
            corrected = re.sub(pattern, replacement, corrected)
            if old_text != corrected and self.debug_mode:
                self.logger.debug(f"增强修正: '{old_text}' -> '{corrected}' ({reason})")
        
        return corrected
    
    def _is_reasonable_percentage(self, value: float) -> bool:
        """
        检查百分比值是否在合理范围内
        
        Args:
            value: 百分比值
            
        Returns:
            是否合理
        """
        # 股票百分比数据通常在 -50% 到 50% 之间
        return -50.0 <= value <= 50.0
    
    def test_raw_ocr_recognition(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        测试原始OCR识别，返回OCR引擎的真实识别结果
        不进行任何数据解析和转换，直接显示OCR识别的原始文本
        使用多种预处理策略获得最佳识别效果
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域尺寸
            
        Returns:
            原始OCR识别结果
        """
        try:
            if self.debug_mode:
                self.logger.debug(f"开始原始OCR测试，区域: x={x}, y={y}, width={width}, height={height}")
            
            # 截取指定区域
            screenshot = self._capture_region(x, y, width, height)
            if screenshot is None:
                return {
                    'success': False,
                    'error': '截图失败',
                    'raw_results': []
                }
            
            # 保存原始截图（调试模式）
            if self.save_debug_images:
                self._save_debug_image(screenshot, f"raw_test_{x}_{y}_{width}_{height}")
            
            # 使用多种预处理策略进行识别，选择最佳结果
            all_raw_results = []
            best_confidence = 0.0
            best_strategy = "unknown"
            
            # 预处理策略列表（按效果排序，优先尝试效果好的）
            preprocessing_strategies = [
                ("无预处理", screenshot),
                ("百分比专用v2(保守)", self._preprocess_for_percentage_v2(screenshot)),
                ("百分比专用v1(标准)", self._preprocess_for_percentage(screenshot)),
                ("仅放大2倍", self._preprocess_scale_only(screenshot, 2.0)),
                ("简化预处理", self._preprocess_image_simple(screenshot)),
                ("仅灰度化", cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)),
                ("CPU优化", self._preprocess_image_cpu_optimized(screenshot)),
            ]
            
            for strategy_name, processed_image in preprocessing_strategies:
                if self.debug_mode:
                    self.logger.debug(f"尝试预处理策略: {strategy_name}")
                
                # 保存预处理后的图像（调试模式）
                if self.save_debug_images:
                    self._save_debug_image(processed_image, f"raw_test_{strategy_name.replace('(', '_').replace(')', '_')}_{x}_{y}_{width}_{height}")
                
                strategy_results = []
                
                # EasyOCR识别
                if self.ocr_engines['easyocr'] and self.reader:
                    try:
                        easyocr_results = self.reader.readtext(processed_image)
                        
                        for result in easyocr_results:
                            bbox, text, confidence = result
                            strategy_results.append({
                                'engine': 'EasyOCR',
                                'strategy': strategy_name,
                                'text': text,
                                'confidence': float(confidence),
                                'bbox': bbox,
                                'position': f"[{bbox[0][0]:.0f},{bbox[0][1]:.0f},{bbox[2][0]-bbox[0][0]:.0f},{bbox[2][1]-bbox[0][1]:.0f}]"
                            })
                            
                            # 更新最佳策略
                            if confidence > best_confidence:
                                best_confidence = confidence
                                best_strategy = f"{strategy_name} + EasyOCR"
                        
                        if self.debug_mode:
                            self.logger.debug(f"EasyOCR({strategy_name})识别到 {len(easyocr_results)} 个文本，最高置信度: {max([r[2] for r in easyocr_results], default=0):.3f}")
                            
                    except Exception as e:
                        self.logger.error(f"EasyOCR({strategy_name})识别失败: {str(e)}")
                        strategy_results.append({
                            'engine': 'EasyOCR',
                            'strategy': strategy_name,
                            'error': str(e),
                            'text': '',
                            'confidence': 0.0
                        })
                
                # PaddleOCR识别
                if self.ocr_engines['paddleocr'] and self.paddle_reader:
                    try:
                        paddle_results = self.paddle_reader.ocr(processed_image, cls=True)
                        
                        if paddle_results and paddle_results[0]:
                            for line in paddle_results[0]:
                                if len(line) >= 2:
                                    bbox = line[0]
                                    text = line[1][0]
                                    confidence = line[1][1]
                                    
                                    strategy_results.append({
                                        'engine': 'PaddleOCR',
                                        'strategy': strategy_name,
                                        'text': text,
                                        'confidence': float(confidence),
                                        'bbox': bbox,
                                        'position': f"[{bbox[0][0]:.0f},{bbox[0][1]:.0f},{bbox[2][0]-bbox[0][0]:.0f},{bbox[2][1]-bbox[0][1]:.0f}]"
                                    })
                                    
                                    # 更新最佳策略
                                    if confidence > best_confidence:
                                        best_confidence = confidence
                                        best_strategy = f"{strategy_name} + PaddleOCR"
                        
                        if self.debug_mode:
                            paddle_count = len(paddle_results[0]) if paddle_results and paddle_results[0] else 0
                            max_confidence = max([line[1][1] for line in paddle_results[0]], default=0) if paddle_results and paddle_results[0] else 0
                            self.logger.debug(f"PaddleOCR({strategy_name})识别到 {paddle_count} 个文本，最高置信度: {max_confidence:.3f}")
                            
                    except Exception as e:
                        self.logger.error(f"PaddleOCR({strategy_name})识别失败: {str(e)}")
                        strategy_results.append({
                            'engine': 'PaddleOCR',
                            'strategy': strategy_name,
                            'error': str(e),
                            'text': '',
                            'confidence': 0.0
                        })
                
                # 将本策略的结果添加到总结果中
                all_raw_results.extend(strategy_results)
                
                # 如果找到了置信度很高的结果，可以提前停止尝试其他策略
                if best_confidence > 0.8:
                    if self.debug_mode:
                        self.logger.debug(f"找到高置信度结果({best_confidence:.3f})，停止尝试其他策略")
                    break
            
            # 按置信度排序
            valid_results = [r for r in all_raw_results if 'error' not in r]
            valid_results.sort(key=lambda x: x['confidence'], reverse=True)
            
            if self.debug_mode:
                self.logger.debug(f"多策略原始OCR测试完成")
                self.logger.debug(f"共尝试 {len(preprocessing_strategies)} 种预处理策略")
                self.logger.debug(f"总识别结果: {len(all_raw_results)} 个，有效: {len(valid_results)} 个")
                self.logger.debug(f"最佳策略: {best_strategy} (置信度: {best_confidence:.3f})")
                if valid_results:
                    self.logger.debug(f"最佳结果: '{valid_results[0]['text']}' (置信度: {valid_results[0]['confidence']:.3f}, {valid_results[0]['strategy']} + {valid_results[0]['engine']})")
            
            return {
                'success': True,
                'raw_results': all_raw_results,
                'valid_count': len(valid_results),
                'total_count': len(all_raw_results),
                'best_strategy': best_strategy,
                'best_confidence': best_confidence,
                'region': {'x': x, 'y': y, 'width': width, 'height': height}
            }
            
        except Exception as e:
            error_msg = f"原始OCR测试失败: {str(e)}"
            self.logger.error(error_msg)
            if self.debug_mode:
                self.logger.debug(f"错误详情: {traceback.format_exc()}")
            
            return {
                'success': False,
                'error': error_msg,
                'raw_results': []
            }
    
    def _preprocess_scale_only(self, image: np.ndarray, scale_factor: float = 2.0) -> np.ndarray:
        """
        仅放大图像的预处理（无其他处理）
        
        Args:
            image: 原始图像
            scale_factor: 放大倍数
            
        Returns:
            放大后的图像
        """
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 放大图像
            height, width = gray.shape
            new_height, new_width = int(height * scale_factor), int(width * scale_factor)
            scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            return scaled
            
        except Exception as e:
            self.logger.error(f"仅放大预处理失败: {str(e)}")
            return image
    
    def test_fund_data_ocr_recognition(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        专门测试多空资金数据OCR识别
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域尺寸
            
        Returns:
            包含识别结果的字典
        """
        try:
            self.logger.info(f"开始测试多空资金OCR识别，区域: ({x}, {y}, {width}, {height})")
            
            # 截取区域
            screenshot = self._capture_region(x, y, width, height)
            if screenshot is None:
                return {
                    'success': False,
                    'error': '无法截取指定区域',
                    'fund_values': [],
                    'raw_results': []
                }
            
            # 保存原始截图（如果启用调试）
            if self.save_debug_images:
                self._save_debug_image(screenshot, f"fund_test_original_{x}_{y}_{width}_{height}")
            
            # 多策略识别文本
            all_raw_texts = []
            fund_values = []
            
            # 定义专门针对百分比数据的预处理策略
            percentage_strategies = [
                ('原始图像', lambda img: cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)),
                ('放大2倍', lambda img: self._preprocess_scale_only(img, 2.0)),
                ('放大3倍', lambda img: self._preprocess_scale_only(img, 3.0)),
                ('百分比专用处理', self._preprocess_for_percentage),
                ('百分比保守处理', self._preprocess_for_percentage_v2),
                ('百分比激进处理', self._preprocess_for_percentage_v3),
            ]
            
            best_strategy = '未知'
            best_confidence = 0.0
            
            for strategy_name, preprocess_func in percentage_strategies:
                if self.debug_mode:
                    self.logger.debug(f"尝试策略: {strategy_name}")
                
                try:
                    # 预处理图像
                    processed_image = preprocess_func(screenshot)
                    
                    # 保存预处理后的图像
                    if self.save_debug_images:
                        self._save_debug_image(processed_image, f"fund_test_{strategy_name.replace(' ', '_')}_{x}_{y}_{width}_{height}")
                    
                    # 使用所有可用引擎识别
                    strategy_texts = []
                    
                    # EasyOCR识别
                    if self.ocr_engines['easyocr'] and self.reader:
                        try:
                            easyocr_results = self.reader.readtext(processed_image)
                            for result in easyocr_results:
                                bbox, text, confidence = result
                                strategy_texts.append({
                                    'engine': 'EasyOCR',
                                    'strategy': strategy_name,
                                    'text': text.strip(),
                                    'confidence': float(confidence)
                                })
                                
                                if confidence > best_confidence:
                                    best_confidence = confidence
                                    best_strategy = f"{strategy_name} + EasyOCR"
                                
                        except Exception as e:
                            self.logger.error(f"EasyOCR({strategy_name})识别失败: {str(e)}")
                    
                    # PaddleOCR识别
                    if self.ocr_engines['paddleocr'] and self.paddle_reader:
                        try:
                            paddle_results = self.paddle_reader.ocr(processed_image, cls=True)
                            if paddle_results and paddle_results[0]:
                                for line in paddle_results[0]:
                                    if len(line) >= 2:
                                        text = line[1][0]
                                        confidence = line[1][1]
                                        strategy_texts.append({
                                            'engine': 'PaddleOCR',
                                            'strategy': strategy_name,
                                            'text': text.strip(),
                                            'confidence': float(confidence)
                                        })
                                        
                                        if confidence > best_confidence:
                                            best_confidence = confidence
                                            best_strategy = f"{strategy_name} + PaddleOCR"
                            
                        except Exception as e:
                            self.logger.error(f"PaddleOCR({strategy_name})识别失败: {str(e)}")
                    
                    all_raw_texts.extend(strategy_texts)
                    
                except Exception as e:
                    self.logger.error(f"策略 {strategy_name} 执行失败: {str(e)}")
            
            # 从所有识别结果中提取多空资金数据
            for result in all_raw_texts:
                text = result['text']
                fund_pct = self._parse_fund_percentage(text)
                if fund_pct is not None:
                    fund_values.append(fund_pct)
            
            # 智能去重：如果同时存在正数和负数的绝对值相同的情况，优先保留负数
            if fund_values:
                # 构建去重后的列表
                unique_values = []
                processed_abs_values = set()
                
                # 先处理所有负数
                for value in fund_values:
                    if value < 0:
                        abs_value = abs(value)
                        if abs_value not in processed_abs_values:
                            unique_values.append(value)
                            processed_abs_values.add(abs_value)
                
                # 再处理正数和零，但跳过已经有负数版本的
                for value in fund_values:
                    if value >= 0:
                        abs_value = abs(value) if value != 0 else 0
                        if abs_value not in processed_abs_values:
                            unique_values.append(value)
                            processed_abs_values.add(abs_value)
                
                fund_values = unique_values
            else:
                fund_values = []
            
            # 提取原始文本（用于调试）
            raw_texts = [result['text'] for result in all_raw_texts if result['text'].strip()]
            raw_texts = list(set(raw_texts))  # 去重
            
            if self.debug_mode:
                self.logger.debug(f"多空资金OCR测试完成")
                self.logger.debug(f"识别到 {len(fund_values)} 个有效多空资金数值")
                self.logger.debug(f"原始文本: {raw_texts}")
                self.logger.debug(f"提取的数值: {fund_values}")
                self.logger.debug(f"最佳策略: {best_strategy} (置信度: {best_confidence:.3f})")
            
            return {
                'success': True,
                'fund_values': fund_values,
                'raw_results': raw_texts,
                'region': {'x': x, 'y': y, 'width': width, 'height': height},
                'best_strategy': best_strategy,
                'best_confidence': best_confidence,
                'strategies_tested': len(percentage_strategies)
            }
            
        except Exception as e:
            error_msg = f"多空资金OCR测试失败: {str(e)}"
            self.logger.error(error_msg)
            if self.debug_mode:
                self.logger.debug(f"错误详情: {traceback.format_exc()}")
            
            return {
                'success': False,
                'error': error_msg,
                'fund_values': [],
                'raw_results': []
            }
    
    def _parse_fund_percentage(self, text: str) -> Optional[float]:
        """
        解析多空资金百分比数据
        
        Args:
            text: OCR识别的文本
            
        Returns:
            解析出的百分比数值（不包含%符号），如果解析失败返回None
        """
        try:
            if not text or not isinstance(text, str):
                return None
            
            # 清理文本
            cleaned_text = text.strip()
            
            # 多空资金数据的正则表达式模式
            # 匹配格式: x.xxx% 或 -x.xxx% （x为0-9的数字，保留3位小数）
            patterns = [
                r'^([+-]?\d+\.\d{3})%$',        # 标准格式: ±x.xxx%
                r'^([+-]?\d+\.\d{1,3})%$',     # 灵活格式: ±x.x%到±x.xxx%
                r'^([+-]?\d+)%$',              # 整数格式: ±x%
                r'([+-]?\d+\.\d{3})%',         # 包含其他字符但有标准百分比
                r'([+-]?\d+\.\d{1,3})%',       # 包含其他字符但有百分比
            ]
            
            for pattern in patterns:
                match = re.search(pattern, cleaned_text)
                if match:
                    try:
                        value = float(match.group(1))
                        # 验证数值范围（多空资金通常在-100%到+100%之间）
                        if -100.0 <= value <= 100.0:
                            return value
                    except ValueError:
                        continue
            
            # 特殊情况：0.000%
            if cleaned_text in ['0.000%', '0%', '+0.000%', '-0.000%']:
                return 0.0
            
            return None
            
        except Exception as e:
            if self.debug_mode:
                self.logger.debug(f"解析多空资金百分比失败: {text} -> {str(e)}")
            return None

def test_fund_ocr():
    """测试多空资金OCR识别"""
    try:
        print("=== OCR引擎测试 ===")
        
        # 创建OCR实例（启用调试模式）
        ocr = FundDataOCR(use_gpu=False, debug_mode=True, save_debug_images=True)
        
        print("OCR引擎初始化成功！")
        
        # 显示引擎状态
        status = ocr.get_engine_status()
        print(f"可用引擎: {status['available_engines']}")
        print(f"调试模式: {status['debug_mode']}")
        print(f"保存调试图像: {status['save_debug_images']}")
        
        # 测试示例图像识别
        print("\n=== 测试示例图像识别 ===")
        test_result = ocr.test_ocr_with_sample_image()
        if test_result['success']:
            print(f"测试成功！识别结果: {test_result['results']}")
        else:
            print(f"测试失败: {test_result['error']}")
        
        # 测试实际屏幕区域（如果需要）
        print("\n=== 测试屏幕区域识别 ===")
        print("如需测试实际屏幕区域，请手动调用 recognize_fund_data_in_region(x, y, width, height)")
        
        # 示例：测试固定区域
        # result = ocr.recognize_fund_data_in_region(100, 100, 300, 200)
        # print(f"屏幕区域识别结果: {result}")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")

def enhanced_test_fund_ocr():
    """增强版OCR测试，包含完整的诊断功能"""
    from ocr_diagnostics import OCRDiagnostics
    
    print("=== 增强版OCR诊断测试 ===")
    
    # 运行完整诊断
    diagnostics = OCRDiagnostics()
    report = diagnostics.run_full_diagnostics()
    
    print(report)
    
    # 测试FundDataOCR
    print("\n=== FundDataOCR专项测试 ===")
    test_fund_ocr()

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行增强版测试
    enhanced_test_fund_ocr()