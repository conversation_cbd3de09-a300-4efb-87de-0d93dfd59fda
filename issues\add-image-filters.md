# 添加图片文件过滤规则任务

**任务时间**: 2025-07-20T19:26:03  
**任务类型**: .gitignore 配置优化  
**执行策略**: 精细化图片过滤策略

## 任务背景

用户需要为股票筛选器项目的 `.gitignore` 文件添加更完整的图片文件过滤规则。项目中存在大量调试图片、OCR测试图片和临时截图文件，需要进行精确的过滤管理。

## 项目现状分析

### 现有图片相关内容
- **调试图片目录**: `debug_images_20250720_*` (多个时间戳目录)
- **图片文件类型**: PNG格式的OCR调试图片
- **已有过滤规则**: `pic/`, `images/`, `screenshots/` 目录已被过滤
- **配置文件设置**: `config.py` 中已配置 `debug_images_dir` 和 `save_debug_images`

### 问题识别
1. 调试图片目录未被完全覆盖
2. 特定的OCR测试图片文件未被过滤
3. 缺少针对不同图片类型的精确分类

## 解决方案

选择**方案2: 精细化图片过滤策略**

### 过滤规则分类
1. **OCR和调试图片**: 
   - `debug_images*/` - 调试图片目录
   - `ocr_debug_images/` - OCR专用调试目录
   - `fund_test_*.png` - OCR测试图片
   
2. **临时和缓存图片**:
   - `temp_*.jpg`, `temp_*.png` - 临时图片
   - `capture_*.png` - 截图文件
   - `cache_*.jpg` - 缓存图片

3. **通用图片格式**:
   - 在临时目录下的各种图片格式
   - 保护可能的项目资源图片

## 执行计划

1. ✅ 分析现有 `.gitignore` 结构
2. 🔄 添加精细化图片过滤规则
3. ⏳ 优化现有过滤规则  
4. ⏳ 验证规则效果

## 预期结果

- 精确过滤调试和临时图片
- 保护重要项目资源
- 保持代码库精简
- 提供清晰的规则分类和注释 