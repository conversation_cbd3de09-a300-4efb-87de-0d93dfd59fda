# -*- coding: utf-8 -*-
"""
指南针连接测试模块
独立的测试工具，用于验证指南针软件连接和功能
"""

import logging
from compass_automator import CompassAutomator


def test_compass_connection() -> bool:
    """
    测试指南针连接（键盘操作模式）
    
    Returns:
        连接是否成功
    """
    try:
        automator = CompassAutomator()
        success = automator.start_compass_software()
        
        if success:
            automator.logger.info("连接测试成功！")
            automator.logger.info("提示：本工具现在使用键盘操作模式，更加稳定可靠")
            automator.close_compass_software()
            return True
        else:
            automator.logger.warning("连接测试失败")
            automator.logger.info("请确保：")
            automator.logger.info("1. 指南针软件已经启动并可见")
            automator.logger.info("2. 软件窗口标题包含 '指南针'、'全赢' 或 '数据分析' 等关键词")
            automator.logger.info("3. 如果使用OCR模式，请确保已配置正确的截图区域")
            return False
            
    except Exception as e:
        logging.error(f"测试指南针连接失败: {str(e)}")
        logging.info("建议：请检查是否安装了必要的依赖包（pywinauto, easyocr等）")
        return False


def test_single_stock_analysis(stock_code: str = "000001") -> dict:
    """
    测试单只股票分析功能
    
    Args:
        stock_code: 要测试的股票代码，默认为000001
        
    Returns:
        测试结果字典
    """
    try:
        automator = CompassAutomator()
        
        if automator.start_compass_software():
            print(f"开始测试股票 {stock_code} 的分析功能...")
            result = automator.analyze_single_stock(stock_code)
            automator.close_compass_software()
            return result
        else:
            return {
                'stock_code': stock_code,
                'status': '连接失败',
                'error': '无法连接到指南针软件'
            }
            
    except Exception as e:
        logging.error(f"测试股票分析失败: {str(e)}")
        return {
            'stock_code': stock_code,
            'status': '测试失败',
            'error': str(e)
        }


def run_comprehensive_test():
    """运行综合测试"""
    print("=" * 50)
    print("指南针自动化工具 - 综合测试")
    print("=" * 50)
    
    # 设置日志级别
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 测试1: 连接测试
    print("\n1. 测试指南针软件连接...")
    connection_success = test_compass_connection()
    if connection_success:
        print("✓ 连接测试通过")
    else:
        print("✗ 连接测试失败")
        return False
    
    # 测试2: 股票分析测试
    print("\n2. 测试股票分析功能...")
    test_stocks = ["000001", "000002", "600000"]
    
    for stock_code in test_stocks:
        print(f"\n测试股票: {stock_code}")
        result = test_single_stock_analysis(stock_code)
        print(f"结果: {result}")
        
        if result.get('status') == '分析成功':
            print(f"✓ 股票 {stock_code} 测试通过")
        else:
            print(f"✗ 股票 {stock_code} 测试失败: {result.get('status', '未知错误')}")
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)


if __name__ == "__main__":
    # 运行测试
    print("启动指南针自动化工具测试...")
    
    # 可以选择运行单个测试或综合测试
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "connection":
            # 只测试连接
            logging.basicConfig(level=logging.INFO)
            print("测试指南针连接...")
            if test_compass_connection():
                print("连接成功！")
            else:
                print("连接失败！")
                
        elif sys.argv[1] == "stock":
            # 测试股票分析
            stock_code = sys.argv[2] if len(sys.argv) > 2 else "000001"
            logging.basicConfig(level=logging.INFO)
            print(f"测试股票分析: {stock_code}")
            result = test_single_stock_analysis(stock_code)
            print(f"测试结果: {result}")
            
        else:
            print("使用方法:")
            print("python compass_tester.py connection  # 测试连接")
            print("python compass_tester.py stock [股票代码]  # 测试股票分析")
            print("python compass_tester.py  # 运行综合测试")
    else:
        # 运行综合测试
        run_comprehensive_test()