# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a stock screening tool (股票筛选器) that automates data extraction from the Compass financial software (指南针全赢数据分析系统). The tool reads stock codes from Excel files, automatically interacts with the Compass desktop application to gather fund flow data, and filters stocks based on configurable criteria.

## System Requirements & Environment

- **Platform**: Windows 10/11 (required for Compass software interaction)
- **Python**: 3.8+
- **External Dependency**: Compass financial software must be installed and configured

## Core Architecture

### Module Structure

1. **`config.py`** - Centralized configuration management
   - `COMPASS_SOFTWARE`: Compass application paths and UI control definitions
   - `APP_CONFIG`: Application behavior settings (OCR mode, timing, retries)
   - `GUI_CONFIG`: GUI layout and styling configuration
   - `OCR_CONFIG`: OCR recognition settings
   - `FILTER_CONFIG`: Stock filtering criteria

2. **`compass_automator.py`** - Windows GUI automation layer
   - `CompassAutomator` class handles all Compass software interactions
   - Dual-mode operation: UI control automation (default) or OCR-based data extraction
   - Manages process lifecycle, window detection, and data extraction

3. **`data_processor.py`** - Data handling and business logic
   - `DataProcessor` class for Excel I/O and stock filtering
   - `parse_fund_value()` function handles Chinese financial notation (万, 亿)
   - Stock validation and filtering logic based on fund flow trends

4. **`main.py`** - Tkinter GUI application
   - `StockScreenerGUI` class provides the main interface
   - Threaded processing to keep GUI responsive
   - Inter-thread communication via message queues

### Data Flow

1. User selects Excel file containing stock codes (first column)
2. `DataProcessor` validates and loads stock codes
3. `CompassAutomator` launches/connects to Compass software
4. For each stock: search → extract fund data → apply filtering criteria
5. Results displayed in GUI table and exportable to Excel

### Key Design Patterns

- **Configuration-driven**: All UI controls, timing, and behavior configurable via `config.py`
- **Dual-mode operation**: Falls back to OCR when UI automation fails
- **Defensive programming**: Extensive error handling and logging throughout
- **Threaded architecture**: GUI remains responsive during long operations

## Common Development Commands

### Setup and Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Quick setup (Windows)
install.bat

# Quick setup (Linux/macOS)
./install.sh
```

### Running and Testing
```bash
# Run the main application
python main.py

# Run comprehensive tests
python test_app.py

# Test specific components
python -c "from compass_automator import CompassAutomator; print('Import successful')"
```

### Development and Debugging
```bash
# Enable debug logging (modify config.py)
APP_CONFIG['log_level'] = 'DEBUG'

# Test Excel file processing
python -c "from data_processor import DataProcessor; dp = DataProcessor(); print(dp.load_excel_file('test.xlsx'))"

# Test fund value parsing
python -c "from data_processor import parse_fund_value; print(parse_fund_value('12.34万'))"
```

## Configuration Management

### Critical Configuration Points

1. **Compass Software Path**: Must be updated in `config.py` for each installation
   ```python
   COMPASS_SOFTWARE['exe_path'] = r'C:\Program Files\Compass\Compass.exe'
   ```

2. **UI Control Mapping**: May need adjustment for different Compass versions
   ```python
   'search_box': {'auto_id': 'SearchBox', 'control_type': 'Edit'}
   ```

3. **OCR Fallback**: Configure region coordinates for OCR-based extraction
   ```python
   'ocr_region': {'x': 100, 'y': 200, 'width': 300, 'height': 100}
   ```

### Filtering Logic

Default filtering criteria: `today_fund > yesterday_fund > day_before_yesterday_fund`
- Configurable via `FILTER_CONFIG['filter_logic']`
- Data validation includes range checking and type validation
- Supports Chinese financial notation (万=10k, 亿=100M)

## Troubleshooting Common Issues

### Compass Connection Issues
- Verify `COMPASS_SOFTWARE['exe_path']` matches actual installation
- Check window title matches `main_window_title` configuration
- Ensure Compass software is not blocked by antivirus

### UI Automation Failures
- Use Windows Inspect tool to verify control IDs
- Switch to OCR mode: `APP_CONFIG['use_ocr_mode'] = True`
- Adjust timing settings: `wait_time`, `page_load_wait`

### Excel Processing Issues
- Stock codes must be in first column
- Supported formats: .xlsx, .xls
- File must not be open in other applications

## Development Guidelines

### Language and Communication
- **All responses must be in Chinese (中文)** - This is a Chinese-language project
- Code comments, documentation, and all communication should be in Chinese
- Variable names and function names can be in English, but all user-facing content must be Chinese

### Code Organization Standards
- **Maximum 500 lines per file** - Any file exceeding 500 lines must be split into separate modules
- When splitting large files, consider logical separation by functionality:
  - GUI components can be split into separate widget classes
  - Data processing can be separated by operation type (reading, filtering, exporting)
  - Configuration can be split by feature area (GUI, automation, OCR)
  - Automation functions can be grouped by interaction type (search, data extraction, window management)

### Module Splitting Strategy
When a file approaches 500 lines, consider these patterns:
- **GUI modules**: Split by UI sections (file handling, results display, controls)
- **Data processing**: Separate by data type (Excel operations, fund value parsing, filtering)
- **Automation**: Group by software interaction phase (connection, navigation, extraction)
- **Configuration**: Organize by component (GUI settings, automation parameters, OCR config)

## Important Notes

- This tool is designed for defensive/analytical purposes only
- All GUI automation respects normal software usage patterns
- OCR processing requires GPU for optimal performance
- Threading model ensures GUI responsiveness during long operations
- Extensive logging aids in debugging automation issues